import 'package:flutter/material.dart';
import '/resources/themes/styles/color_styles.dart';

/* Dark Theme Colors - Velvete Brand Colors (Dark Mode)
|-------------------------------------------------------------------------- */

class DarkThemeColors implements ColorStyles {
  // general
  @override
  Color get background => const Color(0xFF212121); // Dark background
  @override
  Color get backgroundContainer => const Color(0xFF2C2C2C); // Dark containers

  @override
  Color get content => const Color(0xFFE1E1E1); // Light text for dark mode
  @override
  Color get primaryAccent => const Color(0xFFB76E79); // Main brand color: #B76E79

  @override
  Color get surfaceBackground => const Color(0xFF2C2C2C); // Dark surface
  @override
  Color get surfaceContent => const Color(0xFFE1E1E1); // Light text on dark surface

  // app bar
  @override
  Color get appBarBackground => const Color(0xFF2C2C2C); // Dark app bar background
  @override
  Color get appBarPrimaryContent => const Color(0xFFE1E1E1); // Light text on dark app bar

  @override
  Color get inputPrimaryContent => const Color(0xFFE1E1E1); // Light text for inputs

  // buttons
  @override
  Color get buttonBackground => const Color(0xFFB76E79); // Main brand color for buttons: #B76E79
  @override
  Color get buttonContent => const Color(0xFFFFFFFF); // White text on buttons

  // bottom tab bar
  @override
  Color get bottomTabBarBackground => const Color(0xFF2C2C2C); // Dark background for bottom nav

  // bottom tab bar - icons
  @override
  Color get bottomTabBarIconSelected => const Color(0xFFB76E79); // Main brand color for selected icon: #B76E79
  @override
  Color get bottomTabBarIconUnselected => const Color(0xFFE1E1E1); // Light grey for unselected icons

  // bottom tab bar - label
  @override
  Color get bottomTabBarLabelUnselected => const Color(0xFFE1E1E1); // Light grey for unselected labels
  @override
  Color get bottomTabBarLabelSelected => const Color(0xFFB76E79); // Main brand color for selected label: #B76E79

  @override
  Color get buttonSecondaryBackground => const Color(0xFFF4C2C2); // Spare brand color: #F4C2C2

  @override
  Color get buttonSecondaryContent => const Color(0xFF2E3A59); // Deep Navy text on secondary buttons
}

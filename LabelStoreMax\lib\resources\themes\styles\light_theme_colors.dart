import 'package:flutter/material.dart';
import '/resources/themes/styles/color_styles.dart';

/* Light Theme Colors - Velvete Brand Colors
|-------------------------------------------------------------------------- */

class LightThemeColors implements ColorStyles {
  // general

  @override
  Color get background => const Color(0xFFFFFFFF); // Primary Background: #FFFFFF
  @override
  Color get backgroundContainer => const Color(0xFFFFFFFF); // White containers
  @override
  Color get content => const Color(0xFF2E3A59); // Deep Navy for primary text: #2E3A59
  @override
  Color get primaryAccent => const Color(0xFFB76E79); // Main brand color: #B76E79

  @override
  Color get surfaceBackground => const Color(0xFFFFFFFF); // White surface
  @override
  Color get surfaceContent => const Color(0xFF333333); // Dark grey for surface text: #333333

  // app bar
  @override
  Color get appBarBackground => const Color(0xFFFFFFFF); // White app bar background
  @override
  Color get appBarPrimaryContent => const Color(0xFF2E3A59); // Deep Navy for app bar text: #2E3A59

  @override
  Color get inputPrimaryContent => const Color(0xFF333333); // Dark grey for input text: #333333

  // buttons
  @override
  Color get buttonContent => const Color(0xFFFFFFFF); // White text on buttons

  @override
  Color get buttonSecondaryBackground => const Color(0xFFF4C2C2); // Spare brand color: #F4C2C2

  @override
  Color get buttonSecondaryContent => const Color(0xFF2E3A59); // Deep Navy text on secondary buttons

  @override
  Color get buttonBackground => const Color(0xFFB76E79); // Main brand color for buttons: #B76E79

  // bottom tab bar
  @override
  Color get bottomTabBarBackground => const Color(0xFFFFFFFF); // White background for bottom nav

  // bottom tab bar - icons
  @override
  Color get bottomTabBarIconSelected => const Color(0xFFB76E79); // Main brand color for selected icon: #B76E79
  @override
  Color get bottomTabBarIconUnselected => const Color(0xFF333333); // Dark grey for unselected icons: #333333

  // bottom tab bar - label
  @override
  Color get bottomTabBarLabelUnselected => const Color(0xFF333333); // Dark grey for unselected labels: #333333
  @override
  Color get bottomTabBarLabelSelected => const Color(0xFFB76E79); // Main brand color for selected label: #B76E79
}

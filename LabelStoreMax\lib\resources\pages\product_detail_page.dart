//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.



import 'package:flutter/material.dart';
import '/resources/widgets/wishlist_icon_widget.dart';
import '/app/controllers/product_detail_controller.dart';
import '/app/models/cart_line_item.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/buttons.dart';
import '/resources/widgets/velvete_ui.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';

import '/app/models/woocommerce_wrappers/my_product_variation.dart';
import '/app/models/woocommerce_wrappers/my_product.dart';
import '/app/services/woocommerce_service.dart';

class ProductDetailPage extends NyStatefulWidget<ProductDetailController> {
  static RouteView path = ("/product-detail", (_) => ProductDetailPage());

  ProductDetailPage({super.key}) : super(child: () => _ProductDetailState());
}

class _ProductDetailState extends NyPage<ProductDetailPage> {
  MyProduct? _product;

  List<MyProductVariation> _productVariations = [];
  final Map<int, dynamic> _tmpAttributeObj = {};
  // Remove WooSignalApp reference for now

  @override
  get init => () async {
        final data = widget.controller.data();
        WooCommerceService wooCommerceService = WooCommerceService();
        if (data is Map && data.containsKey("productId")) {
          _product = await wooCommerceService.getProduct(data["productId"]);
        } else {
          _product = data;
        }
        widget.controller.product = _product;
        if (_product?.type == "variable") {
          await _fetchProductVariations();
        }
      };

  @override
  LoadingStyle loadingStyle = LoadingStyle.skeletonizer();

  String _getAttributeName(int index) {
    if (_product?.attributes == null || index >= _product!.attributes.length) {
      return "";
    }

    final attribute = _product!.attributes[index];
    // Handle both MyProductAttribute objects and raw Map data
    if (attribute is Map<String, dynamic>) {
      return attribute['name']?.toString() ?? "";
    } else {
      // This should be a MyProductAttribute object with a name property
      try {
        return attribute.name?.toString() ?? "";
      } catch (e) {
        print('⚠️ Error accessing attribute name: $e');
        // Fallback: try to convert to string and extract name
        String attrStr = attribute.toString();
        return attrStr.isNotEmpty ? attrStr : "Unknown Attribute";
      }
    }
  }

  List<String> _getAttributeOptions(int index) {
    if (_product?.attributes == null || index >= _product!.attributes.length) {
      return [];
    }

    final attribute = _product!.attributes[index];
    if (attribute is Map<String, dynamic>) {
      final options = attribute['options'];
      if (options is List) {
        return options.map((option) => option.toString()).toList();
      }
    } else if (attribute != null) {
      // Try to access options property if it exists
      try {
        final options = attribute.options;
        if (options is List) {
          return options.map((option) => option.toString()).toList();
        }
      } catch (e) {
        // Fallback
      }
    }
    return [];
  }

  _fetchProductVariations() async {
    List<MyProductVariation> tmpVariations = [];
    int currentPage = 1;
    WooCommerceService wooCommerceService = WooCommerceService();

    bool isFetching = true;
    if (_product?.id == null) {
      return;
    }
    while (isFetching) {
      List<MyProductVariation> tmp = await wooCommerceService.getProductVariations(
        _product!.id,
        page: currentPage,
        perPage: 100,
      );
      if (tmp.isNotEmpty) {
        tmpVariations.addAll(tmp);
      }

      if (tmp.length >= 100) {
        currentPage += 1;
      } else {
        isFetching = false;
      }
    }
    _productVariations = tmpVariations;
  }

  _modalBottomSheetOptionsForAttribute(int attributeIndex) {
    wsModalBottom(
      context,
      (BuildContext context) => Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: EdgeInsets.all(20),
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.7,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with close button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "${trans("Select")} ${_getAttributeName(attributeIndex)}",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(Icons.close, color: Colors.grey[600]),
                ),
              ],
            ),
            Divider(color: Colors.grey[300]),
            SizedBox(height: 8),
            Expanded(
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _getAttributeOptions(attributeIndex).length,
                itemBuilder: (BuildContext context, int index) {
                  final options = _getAttributeOptions(attributeIndex);
                  final optionValue = options[index];
                  final isSelected = (_tmpAttributeObj.isNotEmpty &&
                      _tmpAttributeObj.containsKey(attributeIndex) &&
                      _tmpAttributeObj[attributeIndex]["value"] == optionValue);

                  return Container(
                    margin: EdgeInsets.symmetric(vertical: 4),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Theme.of(context).primaryColor
                          : Colors.grey[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? Theme.of(context).primaryColor
                            : Colors.grey[300]!,
                        width: isSelected ? 2 : 1,
                      ),
                      boxShadow: isSelected ? [
                        BoxShadow(
                          color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: Offset(0, 2),
                        ),
                      ] : null,
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(12),
                        onTap: () {
                          _tmpAttributeObj[attributeIndex] = {
                            "name": _getAttributeName(attributeIndex),
                            "value": optionValue
                          };
                          Navigator.pop(context);
                          _modalBottomSheetAttributes();
                        },
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  optionValue,
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                                    color: isSelected ? Colors.white : Colors.black87,
                                  ),
                                ),
                              ),
                              if (isSelected)
                                Icon(
                                  Icons.check_circle,
                                  color: Colors.white,
                                  size: 24,
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  _modalBottomSheetAttributes() {
    MyProductVariation? productVariation = widget.controller
        .findProductVariation(
            tmpAttributeObj: _tmpAttributeObj,
            productVariations: _productVariations);
    wsModalBottom(
      context,
      (BuildContext context) => Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: EdgeInsets.all(20),
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  trans("Product Options"),
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(Icons.close, color: Colors.grey[600]),
                ),
              ],
            ),
            Divider(color: Colors.grey[300]),
            SizedBox(height: 8),
            Expanded(
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _product?.attributes.length ?? 0,
                itemBuilder: (BuildContext context, int index) {
                  final isSelected = (_tmpAttributeObj.isNotEmpty &&
                      _tmpAttributeObj.containsKey(index));
                  final attributeName = _getAttributeName(index);

                  return Container(
                    margin: EdgeInsets.symmetric(vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? Theme.of(context).primaryColor
                            : Colors.grey[300]!,
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(12),
                        onTap: () => _modalBottomSheetOptionsForAttribute(index),
                        child: Padding(
                          padding: EdgeInsets.all(16),
                          child: Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      attributeName,
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.black87,
                                      ),
                                    ),
                                    SizedBox(height: 4),
                                    Text(
                                      isSelected
                                          ? _tmpAttributeObj[index]["value"]
                                          : "${trans("Tap to select")} $attributeName",
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: isSelected
                                            ? Theme.of(context).primaryColor
                                            : Colors.grey[600],
                                        fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Icon(
                                isSelected ? Icons.check_circle : Icons.arrow_forward_ios,
                                color: isSelected
                                    ? Theme.of(context).primaryColor
                                    : Colors.grey[400],
                                size: isSelected ? 24 : 16,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            Container(
              decoration: BoxDecoration(
                  border: Border(top: BorderSide(color: Colors.black12, width: 1))),
              padding: EdgeInsets.only(top: 10),
              margin: EdgeInsets.only(bottom: 10),
              child: Column(
          children: <Widget>[
            Text(
              (productVariation != null
                  ? "${trans("Price")}: ${formatStringCurrency(total: productVariation.getSafePrice())}"
                  : (((_product?.attributes.length ==
                              _tmpAttributeObj.values.length) &&
                          productVariation == null)
                      ? trans("This variation is unavailable")
                      : trans("Choose your options"))),
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Text(
              (productVariation != null
                  ? !productVariation.isInStock()
                      ? trans("Out of stock")
                      : ""
                  : ""),
              style: Theme.of(context).textTheme.titleMedium,
            ),
            PrimaryButton(
                title: trans("Add to cart"),
                action: () async {
                  // DEFINITIVE ACTION 2: Fortify the "Add to Cart" Gates - ROBUST VALIDATION
                  if (_product?.id == null || _product?.id == 0) {
                    print('❌ DEFINITIVE ACTION 2: BLOCKED invalid product ID: ${_product?.id}');
                    showToast(
                      title: trans("Error"),
                      description: trans("Invalid product cannot be added to cart. Please try again."),
                      style: ToastNotificationStyleType.danger,
                    );
                    return; // IMMEDIATE STOP - Do not proceed to create CartLineItem
                  }

                  if (_product?.attributes.length !=
                      _tmpAttributeObj.values.length) {
                    showToast(
                        title: trans("Oops"),
                        description: trans("Please select valid options first"),
                        style: ToastNotificationStyleType.warning);
                    return;
                  }

                  if (productVariation == null) {
                    showToast(
                        title: trans("Oops"),
                        description: trans("Product variation does not exist"),
                        style: ToastNotificationStyleType.warning);
                    return;
                  }

                  if (!productVariation.isInStock()) {
                    showToast(
                        title: trans("Sorry"),
                        description: trans("This item is not in stock"),
                        style: ToastNotificationStyleType.warning);
                    return;
                  }

                  List<String> options = [];
                  _tmpAttributeObj.forEach((k, v) {
                    options.add("${v["name"]}: ${v["value"]}");
                  });

                  print('🛒 Creating CartLineItem for variable product: ${_product!.name} (ID: ${_product!.id})');

                  CartLineItem cartLineItem = CartLineItem.fromProductVariation(
                    quantityAmount: widget.controller.quantity,
                    options: options,
                    product: _product!,
                    productVariation: productVariation,
                  );

                  print('✅ CartLineItem created with productId: ${cartLineItem.productId}');

                  await widget.controller.itemAddToCart(
                    cartLineItem: cartLineItem,
                  );
                  pop();
                }),
              ],
            ),
          ),
          ],
        ),
      ),
    );
  }

  @override
  Widget view(BuildContext context) {
    if (_product == null) {
      return Scaffold(
        body: Center(
          child: CircularProgressIndicator(
            color: const Color(0xFFB76E79),
          ),
        ),
      );
    }

    return Scaffold(
      appBar: _buildAppBar(context),
      body: Stack(
        children: [
          // Main content
          SingleChildScrollView(
            child: Column(
              children: [
                _buildImageGallery(context),
                _buildProductInfo(context),
                _buildCollapsibleSections(context),
                SizedBox(height: 100), // Space for bottom action bar
              ],
            ),
          ),
          // Sticky bottom action bar
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: _buildBottomActionBar(context),
          ),
        ],
      ),
    );
  }

  _addItemToCart() async {
    // DEFINITIVE ACTION 2: Fortify the "Add to Cart" Gates - ROBUST VALIDATION
    if (_product?.id == null || _product?.id == 0) {
      print('❌ DEFINITIVE ACTION 2: BLOCKED invalid product ID: ${_product?.id}');
      showToast(
        title: trans("Error"),
        description: trans("Invalid product cannot be added to cart. Please try again."),
        style: ToastNotificationStyleType.danger,
      );
      return; // IMMEDIATE STOP - Do not proceed to create CartLineItem
    }

    if (_product?.type != "simple") {
      _modalBottomSheetAttributes();
      return;
    }
    if (_product?.stockStatus != "instock") {
      showToast(
          title: trans("Sorry"),
          description: trans("This item is out of stock"),
          style: ToastNotificationStyleType.warning,
          icon: Icons.local_shipping);
      return;
    }

    print('🛒 Adding to cart: ${_product!.name} (ID: ${_product!.id})');

    await widget.controller.itemAddToCart(
      cartLineItem: CartLineItem.fromProduct(
          quantityAmount: widget.controller.quantity, product: _product!),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back,
          color: Theme.of(context).iconTheme.color,
        ),
        onPressed: () => Navigator.pop(context),
      ),
      actions: [
        IconButton(
          icon: Icon(
            Icons.more_vert,
            color: Theme.of(context).iconTheme.color,
          ),
          onPressed: () {
            // Show more options
            _showMoreOptions(context);
          },
        ),
        WishlistIcon(_product),
        IconButton(
          icon: Icon(
            Icons.close,
            color: Theme.of(context).iconTheme.color,
          ),
          onPressed: () => Navigator.pop(context),
        ),
      ],
    );
  }

  Widget _buildImageGallery(BuildContext context) {
    List<String> images = [];

    // Add main image
    final productImages = _product?.images;
    if (productImages?.isNotEmpty == true) {
      images.addAll(productImages!.map((img) => img.src ?? '').where((src) => src.isNotEmpty));
    }

    // If no images, add placeholder
    if (images.isEmpty) {
      images.add(''); // Placeholder
    }

    return Container(
      height: 400,
      child: PageView.builder(
        itemCount: images.length,
        itemBuilder: (context, index) {
          return Container(
            margin: EdgeInsets.symmetric(horizontal: 8),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(16),
            ),
            child: images[index].isNotEmpty
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: Image.network(
                      images[index],
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return _buildImagePlaceholder();
                      },
                    ),
                  )
                : _buildImagePlaceholder(),
          );
        },
      ),
    );
  }

  Widget _buildImagePlaceholder() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(16),
      ),
      child: Center(
        child: Icon(
          Icons.image,
          size: 80,
          color: Colors.grey[400],
        ),
      ),
    );
  }

  Widget _buildProductInfo(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product Name
          Text(
            _product?.name ?? '',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2E3A59),
                ),
            textAlign: TextAlign.right,
          ),

          SizedBox(height: 8),

          // SKU and Availability
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _product?.stockStatus == 'instock' ? 'متوفر' : 'غير متوفر',
                style: TextStyle(
                  color: _product?.stockStatus == 'instock'
                      ? Colors.green
                      : Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
              if (_product?.sku?.isNotEmpty == true)
                Text(
                  'رمز المنتج: ${_product!.sku}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                ),
            ],
          ),

          SizedBox(height: 16),

          // Price
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildQuantitySelector(),
              _buildPriceDisplay(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPriceDisplay() {
    String price = '';
    if (_product?.onSale == true) {
      price = '${_product?.salePrice} د.ل';
    } else {
      price = '${_product?.price} د.ل';
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          price,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: const Color(0xFFB76E79),
          ),
        ),
        if (_product?.onSale == true)
          Text(
            '${_product?.regularPrice} د.ل',
            style: TextStyle(
              fontSize: 16,
              decoration: TextDecoration.lineThrough,
              color: Colors.grey[600],
            ),
          ),
      ],
    );
  }

  Widget _buildQuantitySelector() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            onPressed: () => widget.controller.removeQuantityTapped(),
            icon: Icon(Icons.remove, color: const Color(0xFFB76E79)),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              '${widget.controller.quantity}',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            onPressed: () => widget.controller.addQuantityTapped(),
            icon: Icon(Icons.add, color: const Color(0xFFB76E79)),
          ),
        ],
      ),
    );
  }

  Widget _buildCollapsibleSections(BuildContext context) {
    return Column(
      children: [
        _buildCollapsibleSection(
          title: 'العلامة التجارية',
          content: _product?.name ?? 'غير محدد',
          icon: Icons.business,
        ),
        _buildCollapsibleSection(
          title: 'الوصف',
          content: _buildDescriptionWidget(),
          icon: Icons.description,
          isWidget: true,
        ),
        _buildCollapsibleSection(
          title: 'معلومات إضافية',
          content: _buildAdditionalInfo(),
          icon: Icons.info,
          isWidget: true,
        ),
      ],
    );
  }

  Widget _buildCollapsibleSection({
    required String title,
    required dynamic content,
    required IconData icon,
    bool isWidget = false,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: ExpansionTile(
        leading: Icon(
          icon,
          color: const Color(0xFFB76E79),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: const Color(0xFF2E3A59),
          ),
        ),
        children: [
          Padding(
            padding: EdgeInsets.all(16),
            child: isWidget
                ? content as Widget
                : Text(
                    content as String,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          height: 1.5,
                        ),
                    textAlign: TextAlign.right,
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Handle attributes safely
        if (_product?.attributes?.isNotEmpty == true)
          ...(_product?.attributes ?? []).map((attr) {
            String attributeName = '';
            String attributeOptions = '';

            try {
              // Handle dynamic attribute structure safely
              if (attr is Map<String, dynamic>) {
                attributeName = attr['name']?.toString() ?? '';
                final options = attr['options'];
                if (options is List) {
                  attributeOptions = options.map((opt) => opt?.toString() ?? '').join(', ');
                } else if (options != null) {
                  attributeOptions = options.toString();
                }
              } else {
                // Try to access as object with properties
                try {
                  attributeName = attr.name?.toString() ?? '';
                  final options = attr.options;
                  if (options is List) {
                    attributeOptions = options.map((opt) => opt?.toString() ?? '').join(', ');
                  } else if (options != null) {
                    attributeOptions = options.toString();
                  }
                } catch (e) {
                  // Fallback: convert entire attribute to string
                  attributeName = 'خاصية';
                  attributeOptions = attr.toString();
                }
              }
            } catch (e) {
              print('⚠️ Error processing attribute: $e');
              attributeName = 'خاصية';
              attributeOptions = 'غير محدد';
            }

            // Only show if we have meaningful data
            if (attributeName.isNotEmpty || attributeOptions.isNotEmpty) {
              return Padding(
                padding: EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        attributeOptions.isNotEmpty ? attributeOptions : 'غير محدد',
                        style: TextStyle(color: Colors.grey[600]),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    SizedBox(width: 8),
                    Text(
                      attributeName.isNotEmpty ? attributeName : 'خاصية',
                      style: TextStyle(fontWeight: FontWeight.w600),
                      textAlign: TextAlign.right,
                    ),
                  ],
                ),
              );
            } else {
              return SizedBox.shrink();
            }
          }).where((widget) => widget is! SizedBox),

        // Handle categories safely
        if (_product?.categories?.isNotEmpty == true)
          Padding(
            padding: EdgeInsets.symmetric(vertical: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    _product!.categories!.map((cat) => cat.name).join(', '),
                    style: TextStyle(color: Colors.grey[600]),
                    textAlign: TextAlign.left,
                  ),
                ),
                SizedBox(width: 8),
                Text(
                  'الفئات',
                  style: TextStyle(fontWeight: FontWeight.w600),
                  textAlign: TextAlign.right,
                ),
              ],
            ),
          ),

        // Show SKU if available
        if (_product?.sku?.isNotEmpty == true)
          Padding(
            padding: EdgeInsets.symmetric(vertical: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    _product!.sku!,
                    style: TextStyle(color: Colors.grey[600]),
                    textAlign: TextAlign.left,
                  ),
                ),
                SizedBox(width: 8),
                Text(
                  'رمز المنتج',
                  style: TextStyle(fontWeight: FontWeight.w600),
                  textAlign: TextAlign.right,
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildBottomActionBar(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Buy Now Button (Secondary)
          Expanded(
            child: OutlinedButton(
              onPressed: () {
                _addItemToCart();
                // Navigate to cart after adding
                Navigator.pushNamed(context, '/cart');
              },
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: const Color(0xFFB76E79)),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text(
                'اشتر الآن',
                style: TextStyle(
                  color: const Color(0xFFB76E79),
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),

          SizedBox(width: 12),

          // Add to Cart Button (Primary)
          Expanded(
            child: ElevatedButton(
              onPressed: _addItemToCart,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFB76E79),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: EdgeInsets.symmetric(vertical: 16),
                elevation: 4,
              ),
              child: Text(
                'أضف إلى السلة',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showMoreOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: Icon(Icons.share, color: const Color(0xFFB76E79)),
                title: Text('مشاركة المنتج'),
                onTap: () {
                  Navigator.pop(context);
                  // Implement share functionality
                },
              ),
              ListTile(
                leading: Icon(Icons.report, color: const Color(0xFFB76E79)),
                title: Text('الإبلاغ عن مشكلة'),
                onTap: () {
                  Navigator.pop(context);
                  // Implement report functionality
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDescriptionWidget() {
    String description = _product?.description ?? '';

    if (description.isEmpty) {
      return Text(
        'لا يوجد وصف متاح',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
              height: 1.5,
            ),
        textAlign: TextAlign.right,
      );
    }

    // Check if description contains HTML tags
    if (description.contains('<') && description.contains('>')) {
      return HtmlWidget(
        description,
        textStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
              height: 1.5,
            ),
        customStylesBuilder: (element) {
          if (element.localName == 'p') {
            return {'text-align': 'right', 'direction': 'rtl'};
          }
          return null;
        },
      );
    } else {
      // Plain text description
      return Text(
        description,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              height: 1.5,
            ),
        textAlign: TextAlign.right,
      );
    }
  }
}
